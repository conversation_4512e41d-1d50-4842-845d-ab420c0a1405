<?php

namespace App\Services;

use Google_Client;
use Google_Service_Gmail;
use Google_Service_Gmail_Message;
use Google_Service_Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\GmailApiException;

class GmailApiService
{
    private $client;
    private $service;
    private $serviceAccountEmail;

    public function __construct()
    {
        $this->initializeClient();
    }

    /**
     * Initialize the Google Client with service account authentication
     */
    private function initializeClient()
    {
        try {
            $this->client = new Google_Client();

            // Set the service account credentials
            $credentialsPath = storage_path('app/private/gmail-service-account.json');

            if (!file_exists($credentialsPath)) {
                throw new GmailApiException('Gmail service account credentials file not found', [
                    'credentials_path' => $credentialsPath
                ]);
            }

            // Validate credentials file
            $credentialsContent = file_get_contents($credentialsPath);
            $credentials = json_decode($credentialsContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new GmailApiException('Invalid JSON in Gmail service account credentials file', [
                    'json_error' => json_last_error_msg(),
                    'credentials_path' => $credentialsPath
                ]);
            }

            // Validate required fields
            $requiredFields = ['type', 'client_email', 'private_key', 'project_id'];
            foreach ($requiredFields as $field) {
                if (!isset($credentials[$field]) || empty($credentials[$field])) {
                    throw new GmailApiException("Missing required field '$field' in Gmail service account credentials", [
                        'missing_field' => $field,
                        'credentials_path' => $credentialsPath
                    ]);
                }
            }

            $this->client->setAuthConfig($credentialsPath);
            $this->client->addScope(Google_Service_Gmail::GMAIL_SEND);

            // Set the subject for domain-wide delegation
            $subjectEmail = config('mail.from.address');
            if (empty($subjectEmail)) {
                throw new GmailApiException('MAIL_FROM_ADDRESS configuration is required for Gmail API');
            }

            $this->client->setSubject($subjectEmail);

            $this->serviceAccountEmail = $credentials['client_email'];
            $this->service = new Google_Service_Gmail($this->client);

            Log::info('Gmail API Service initialized successfully', [
                'service_account_email' => $this->serviceAccountEmail,
                'subject_email' => $subjectEmail,
                'project_id' => $credentials['project_id']
            ]);
        } catch (GmailApiException $e) {
            $e->report();
            throw $e;
        } catch (\Exception $e) {
            Log::error('Unexpected error while initializing Gmail API Service', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new GmailApiException('Failed to initialize Gmail API Service', [], 0, $e);
        }
    }

    /**
     * Send an email using Gmail API
     *
     * @param string $to
     * @param string $subject
     * @param string $body
     * @param string $fromEmail
     * @param string $fromName
     * @param array $attachments
     * @return bool
     */
    public function sendEmail($to, $subject, $body, $fromEmail = null, $fromName = null, $attachments = [])
    {
        try {
            // Validate inputs
            if (empty($to)) {
                throw new GmailApiException('Recipient email address is required', ['to' => $to]);
            }

            if (empty($subject)) {
                throw new GmailApiException('Email subject is required', ['subject' => $subject]);
            }

            $fromEmail = $fromEmail ?: config('mail.from.address');
            $fromName = $fromName ?: config('mail.from.name');

            // Validate from email
            if (empty($fromEmail)) {
                throw new GmailApiException('From email address is required', ['from_email' => $fromEmail]);
            }

            // Create the email message
            $message = $this->createMessage($to, $subject, $body, $fromEmail, $fromName, $attachments);

            // Send the message
            $result = $this->service->users_messages->send('me', $message);

            Log::info('Email sent successfully via Gmail API', [
                'to' => $to,
                'subject' => $subject,
                'from' => $fromEmail,
                'message_id' => $result->getId(),
                'timestamp' => now()->toISOString()
            ]);

            return true;
        } catch (Google_Service_Exception $e) {
            $context = [
                'to' => $to,
                'subject' => $subject,
                'from' => $fromEmail ?? 'unknown',
                'google_error_code' => $e->getCode(),
                'google_error_message' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ];

            Log::error('Google Service Exception while sending email via Gmail API', $context);

            return false;
        } catch (GmailApiException $e) {
            $e->report();
            return false;
        } catch (\Exception $e) {
            Log::error('Unexpected error while sending email via Gmail API', [
                'to' => $to,
                'subject' => $subject,
                'from' => $fromEmail ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'timestamp' => now()->toISOString()
            ]);

            return false;
        }
    }

    /**
     * Create a Gmail message
     *
     * @param string $to
     * @param string $subject
     * @param string $body
     * @param string $fromEmail
     * @param string $fromName
     * @param array $attachments
     * @return Google_Service_Gmail_Message
     */
    private function createMessage($to, $subject, $body, $fromEmail, $fromName, $attachments = [])
    {
        $boundary = uniqid(rand(), true);

        // Build the email headers
        $headers = [
            'To' => $to,
            'From' => $fromName ? "$fromName <$fromEmail>" : $fromEmail,
            'Subject' => $subject,
            'MIME-Version' => '1.0',
            'Content-Type' => 'multipart/mixed; boundary="' . $boundary . '"'
        ];

        $rawMessage = '';
        foreach ($headers as $key => $value) {
            $rawMessage .= "$key: $value\r\n";
        }
        $rawMessage .= "\r\n";

        // Add the body
        $rawMessage .= "--$boundary\r\n";
        $rawMessage .= "Content-Type: text/html; charset=UTF-8\r\n";
        $rawMessage .= "Content-Transfer-Encoding: quoted-printable\r\n\r\n";
        $rawMessage .= quoted_printable_encode($body) . "\r\n";

        // Add attachments if any
        foreach ($attachments as $attachment) {
            $rawMessage .= "--$boundary\r\n";
            $rawMessage .= "Content-Type: " . $attachment['mime_type'] . "; name=\"" . $attachment['name'] . "\"\r\n";
            $rawMessage .= "Content-Disposition: attachment; filename=\"" . $attachment['name'] . "\"\r\n";
            $rawMessage .= "Content-Transfer-Encoding: base64\r\n\r\n";
            $rawMessage .= chunk_split(base64_encode($attachment['content'])) . "\r\n";
        }

        $rawMessage .= "--$boundary--";

        // Create the message
        $message = new Google_Service_Gmail_Message();
        $message->setRaw(base64url_encode($rawMessage));

        return $message;
    }

    /**
     * Base64url encode
     */
    private function base64url_encode($data)
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * Test the Gmail API connection
     *
     * @return bool
     */
    public function testConnection()
    {
        try {
            $profile = $this->service->users->getProfile('me');
            Log::info('Gmail API connection test successful', [
                'email_address' => $profile->getEmailAddress()
            ]);
            return true;
        } catch (\Exception $e) {
            Log::error('Gmail API connection test failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get service account email
     *
     * @return string
     */
    public function getServiceAccountEmail()
    {
        return $this->serviceAccountEmail;
    }
}
